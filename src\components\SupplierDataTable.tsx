import React, { useState, useMemo } from 'react';
import { 
  Search, 
  Download, 
  Plus, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  Calendar,
  Building2,
  Package
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

// Types for our supplier data
export interface DataCollectionCategory {
  process: number; // 0-100 percentage
  energyConsumption: number;
  specialProcessMaterial: number;
  waste: number;
  transportation: number;
}

export interface SupplierData {
  id: string;
  supplier: string;
  vendorCode: string;
  partNumber: string;
  description: string;
  imdsId: string;
  lastUpdated: Date;
  dataCollectionStatus: DataCollectionCategory;
}

interface SupplierDataTableProps {
  data: SupplierData[];
  onAddNew?: () => void;
  onExport?: () => void;
}

type SortField = keyof Omit<SupplierData, 'dataCollectionStatus'> | 'overallProgress';
type SortDirection = 'asc' | 'desc' | null;

// Component for displaying the multi-category data collection status
const DataCollectionStatusDisplay: React.FC<{ status: DataCollectionCategory }> = ({ status }) => {
  const categories = [
    { key: 'process', label: 'Process', value: status.process },
    { key: 'energyConsumption', label: 'Energy', value: status.energyConsumption },
    { key: 'specialProcessMaterial', label: 'Material', value: status.specialProcessMaterial },
    { key: 'waste', label: 'Waste', value: status.waste },
    { key: 'transportation', label: 'Transport', value: status.transportation },
  ];

  return (
    <div className="w-full min-w-96 space-y-3">
      <div className="grid grid-cols-5 gap-2 text-xs font-medium text-muted-foreground">
        {categories.map((category) => (
          <div key={category.key} className="text-center truncate" title={category.label}>
            {category.label}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-5 gap-2">
        {categories.map((category) => (
          <div key={category.key} className="flex flex-col items-center gap-1">
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div 
                className="h-full bg-secondary transition-all duration-300"
                style={{ width: `${category.value}%` }}
              />
            </div>
            <span className="text-xs text-muted-foreground">
              {category.value}%
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export const SupplierDataTable: React.FC<SupplierDataTableProps> = ({ 
  data, 
  onAddNew, 
  onExport 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let filtered = data.filter(item => {
      const matchesSearch = Object.values(item).some(value => {
        if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
          return Object.values(value).some(v => v.toString().toLowerCase().includes(searchTerm.toLowerCase()));
        }
        return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
      });
      
      // For status filtering, calculate overall completion
      const avgCompletion = (
        item.dataCollectionStatus.process +
        item.dataCollectionStatus.energyConsumption +
        item.dataCollectionStatus.specialProcessMaterial +
        item.dataCollectionStatus.waste +
        item.dataCollectionStatus.transportation
      ) / 5;
      
      let matchesStatus = statusFilter === 'all';
      if (statusFilter === 'completed') matchesStatus = avgCompletion >= 90;
      else if (statusFilter === 'pending') matchesStatus = avgCompletion >= 30 && avgCompletion < 90;
      else if (statusFilter === 'overdue') matchesStatus = avgCompletion >= 10 && avgCompletion < 30;
      else if (statusFilter === 'not-started') matchesStatus = avgCompletion < 10;
      
      return matchesSearch && matchesStatus;
    });

    if (sortField && sortDirection) {
      filtered.sort((a, b) => {
        let aValue: any;
        let bValue: any;
        
        if (sortField === 'overallProgress') {
          aValue = (a.dataCollectionStatus.process + a.dataCollectionStatus.energyConsumption + 
                   a.dataCollectionStatus.specialProcessMaterial + a.dataCollectionStatus.waste + 
                   a.dataCollectionStatus.transportation) / 5;
          bValue = (b.dataCollectionStatus.process + b.dataCollectionStatus.energyConsumption + 
                   b.dataCollectionStatus.specialProcessMaterial + b.dataCollectionStatus.waste + 
                   b.dataCollectionStatus.transportation) / 5;
        } else {
          aValue = a[sortField];
          bValue = b[sortField];
        }

        if (aValue instanceof Date && bValue instanceof Date) {
          const aTime = aValue.getTime();
          const bTime = bValue.getTime();
          if (aTime < bTime) return sortDirection === 'asc' ? -1 : 1;
          if (aTime > bTime) return sortDirection === 'asc' ? 1 : -1;
          return 0;
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [data, searchTerm, sortField, sortDirection, statusFilter]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedData.length / itemsPerPage);
  const paginatedData = filteredAndSortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(
        sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc'
      );
      if (sortDirection === 'desc') {
        setSortField(null);
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    if (sortDirection === 'asc') return <ArrowUp className="h-4 w-4" />;
    if (sortDirection === 'desc') return <ArrowDown className="h-4 w-4" />;
    return <ArrowUpDown className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6 w-full max-w-none">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Supplier LCA Data</h2>
          <p className="text-muted-foreground">
            Track and manage supplier lifecycle assessment information
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="professional" onClick={onExport}>
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="corporate" onClick={onAddNew}>
            <Plus className="h-4 w-4" />
            Add Supplier
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search suppliers, parts, or codes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background text-foreground"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="overdue">Overdue</option>
                <option value="not-started">Not Started</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b bg-muted/50">
                <tr>
                  <th className="text-left p-4 font-medium">
                    <button
                      onClick={() => handleSort('supplier')}
                      className="flex items-center gap-2 hover:text-primary transition-colors"
                    >
                      <Building2 className="h-4 w-4" />
                      Supplier
                      {getSortIcon('supplier')}
                    </button>
                  </th>
                  <th className="text-left p-4 font-medium">
                    <button
                      onClick={() => handleSort('vendorCode')}
                      className="flex items-center gap-2 hover:text-primary transition-colors"
                    >
                      Vendor Code
                      {getSortIcon('vendorCode')}
                    </button>
                  </th>
                  <th className="text-left p-4 font-medium">
                    <button
                      onClick={() => handleSort('partNumber')}
                      className="flex items-center gap-2 hover:text-primary transition-colors"
                    >
                      <Package className="h-4 w-4" />
                      Part Number
                      {getSortIcon('partNumber')}
                    </button>
                  </th>
                  <th className="text-left p-4 font-medium">Description</th>
                  <th className="text-left p-4 font-medium">
                    <button
                      onClick={() => handleSort('imdsId')}
                      className="flex items-center gap-2 hover:text-primary transition-colors"
                    >
                      IMDS ID
                      {getSortIcon('imdsId')}
                    </button>
                  </th>
                  <th className="text-left p-4 font-medium">
                    <button
                      onClick={() => handleSort('lastUpdated')}
                      className="flex items-center gap-2 hover:text-primary transition-colors"
                    >
                      <Calendar className="h-4 w-4" />
                      Last Updated
                      {getSortIcon('lastUpdated')}
                    </button>
                  </th>
                  <th className="text-left p-4 font-medium">
                    <button
                      onClick={() => handleSort('overallProgress')}
                      className="flex items-center gap-2 hover:text-primary transition-colors"
                    >
                      Data Collection Status
                      {getSortIcon('overallProgress')}
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.map((item) => {
                  return (
                    <tr 
                      key={item.id} 
                      className="border-b hover:bg-muted/50 transition-colors"
                    >
                      <td className="p-4 font-medium">{item.supplier}</td>
                      <td className="p-4 font-mono text-sm">{item.vendorCode}</td>
                      <td className="p-4 font-mono text-sm">{item.partNumber}</td>
                      <td className="p-4 max-w-xs truncate" title={item.description}>
                        {item.description}
                      </td>
                      <td className="p-4 font-mono text-sm">{item.imdsId || '-'}</td>
                      <td className="p-4 text-sm">
                        {item.lastUpdated.toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        <DataCollectionStatusDisplay status={item.dataCollectionStatus} />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center">
          <p className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
            {Math.min(currentPage * itemsPerPage, filteredAndSortedData.length)} of{' '}
            {filteredAndSortedData.length} entries
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};