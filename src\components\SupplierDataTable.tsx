import React, { useState, useMemo } from 'react';
import {
  Search,
  Download,
  Plus,
  Filter,
  Calendar,
  Building2,
  Package,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';

// Types for our supplier data
export interface DataCollectionCategory {
  process: number; // 0-100 percentage
  energyConsumption: number;
  specialProcessMaterial: number;
  waste: number;
  transportation: number;
}

export interface SupplierData {
  id: string;
  supplier: string;
  vendorCode: string;
  partNumber: string;
  description: string;
  imdsId: string;
  lastUpdated: Date;
  dataCollectionStatus: DataCollectionCategory;
}

interface SupplierDataTableProps {
  data: SupplierData[];
  onAddNew?: () => void;
  onExport?: () => void;
}

interface ColumnFilters {
  supplier: string;
  vendorCode: string;
  partNumber: string;
  description: string;
  imdsId: string;
  lastUpdated: string;
  dataCollectionStatus: string;
}

interface FilterDropdownState {
  supplier: boolean;
  vendorCode: boolean;
  partNumber: boolean;
  description: boolean;
  imdsId: boolean;
  lastUpdated: boolean;
  dataCollectionStatus: boolean;
}

// Filter Dropdown Component
const FilterDropdown: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-0 mt-1 z-50 bg-white border border-gray-200 rounded-md shadow-lg p-3 min-w-48">
      <div className="flex justify-between items-center mb-2">
        <span className="text-xs font-medium text-gray-600">Filter</span>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <X className="h-3 w-3" />
        </button>
      </div>
      {children}
    </div>
  );
};

// Component for displaying the multi-category data collection status
const DataCollectionStatusDisplay: React.FC<{ status: DataCollectionCategory }> = ({ status }) => {
  const categories = [
    { key: 'process', label: 'Process', value: status.process },
    { key: 'energyConsumption', label: 'Energy', value: status.energyConsumption },
    { key: 'specialProcessMaterial', label: 'Material', value: status.specialProcessMaterial },
    { key: 'waste', label: 'Waste', value: status.waste },
    { key: 'transportation', label: 'Transport', value: status.transportation },
  ];

  return (
    <div className="w-full min-w-96 space-y-3">
      <div className="grid grid-cols-5 gap-2 text-xs font-medium text-muted-foreground">
        {categories.map((category) => (
          <div key={category.key} className="text-center truncate" title={category.label}>
            {category.label}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-5 gap-2">
        {categories.map((category) => (
          <div key={category.key} className="flex flex-col items-center gap-1">
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div 
                className="h-full bg-secondary transition-all duration-300"
                style={{ width: `${category.value}%` }}
              />
            </div>
            <span className="text-xs text-muted-foreground">
              {category.value}%
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export const SupplierDataTable: React.FC<SupplierDataTableProps> = ({
  data,
  onAddNew,
  onExport
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [columnFilters, setColumnFilters] = useState<ColumnFilters>({
    supplier: '',
    vendorCode: '',
    partNumber: '',
    description: '',
    imdsId: '',
    lastUpdated: '',
    dataCollectionStatus: ''
  });
  const [filterDropdowns, setFilterDropdowns] = useState<FilterDropdownState>({
    supplier: false,
    vendorCode: false,
    partNumber: false,
    description: false,
    imdsId: false,
    lastUpdated: false,
    dataCollectionStatus: false
  });
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter data
  const filteredData = useMemo(() => {
    return data.filter(item => {
      // Global search filter
      const matchesSearch = Object.values(item).some(value => {
        if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
          return Object.values(value).some(v => v.toString().toLowerCase().includes(searchTerm.toLowerCase()));
        }
        return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
      });

      // Column-specific filters
      const matchesSupplier = !columnFilters.supplier ||
        item.supplier.toLowerCase().includes(columnFilters.supplier.toLowerCase());

      const matchesVendorCode = !columnFilters.vendorCode ||
        item.vendorCode.toLowerCase().includes(columnFilters.vendorCode.toLowerCase());

      const matchesPartNumber = !columnFilters.partNumber ||
        item.partNumber.toLowerCase().includes(columnFilters.partNumber.toLowerCase());

      const matchesDescription = !columnFilters.description ||
        item.description.toLowerCase().includes(columnFilters.description.toLowerCase());

      const matchesImdsId = !columnFilters.imdsId ||
        (item.imdsId && item.imdsId.toLowerCase().includes(columnFilters.imdsId.toLowerCase()));

      const matchesLastUpdated = !columnFilters.lastUpdated ||
        item.lastUpdated.toLocaleDateString().includes(columnFilters.lastUpdated);

      // Data collection status filter
      const avgCompletion = (
        item.dataCollectionStatus.process +
        item.dataCollectionStatus.energyConsumption +
        item.dataCollectionStatus.specialProcessMaterial +
        item.dataCollectionStatus.waste +
        item.dataCollectionStatus.transportation
      ) / 5;

      let matchesDataCollectionStatus = !columnFilters.dataCollectionStatus;
      if (columnFilters.dataCollectionStatus) {
        const filter = columnFilters.dataCollectionStatus.toLowerCase();
        if (filter === 'completed') matchesDataCollectionStatus = avgCompletion >= 90;
        else if (filter === 'pending') matchesDataCollectionStatus = avgCompletion >= 30 && avgCompletion < 90;
        else if (filter === 'overdue') matchesDataCollectionStatus = avgCompletion >= 10 && avgCompletion < 30;
        else if (filter === 'not-started') matchesDataCollectionStatus = avgCompletion < 10;
      }

      // Status filter (existing functionality)
      let matchesStatus = statusFilter === 'all';
      if (statusFilter === 'completed') matchesStatus = avgCompletion >= 90;
      else if (statusFilter === 'pending') matchesStatus = avgCompletion >= 30 && avgCompletion < 90;
      else if (statusFilter === 'overdue') matchesStatus = avgCompletion >= 10 && avgCompletion < 30;
      else if (statusFilter === 'not-started') matchesStatus = avgCompletion < 10;

      return matchesSearch && matchesSupplier && matchesVendorCode && matchesPartNumber &&
             matchesDescription && matchesImdsId && matchesLastUpdated && matchesDataCollectionStatus && matchesStatus;
    });
  }, [data, searchTerm, columnFilters, statusFilter]);

  // Pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle column filter changes
  const handleColumnFilterChange = (column: keyof ColumnFilters, value: string) => {
    setColumnFilters(prev => ({
      ...prev,
      [column]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Toggle filter dropdown
  const toggleFilterDropdown = (column: keyof FilterDropdownState) => {
    setFilterDropdowns(prev => ({
      ...prev,
      [column]: !prev[column]
    }));
  };

  // Close all filter dropdowns
  const closeAllDropdowns = () => {
    setFilterDropdowns({
      supplier: false,
      vendorCode: false,
      partNumber: false,
      description: false,
      imdsId: false,
      lastUpdated: false,
      dataCollectionStatus: false
    });
  };

  // Clear all column filters
  const clearAllFilters = () => {
    setColumnFilters({
      supplier: '',
      vendorCode: '',
      partNumber: '',
      description: '',
      imdsId: '',
      lastUpdated: '',
      dataCollectionStatus: ''
    });
    setSearchTerm('');
    setStatusFilter('all');
    setCurrentPage(1);
    closeAllDropdowns();
  };

  return (
    <div className="space-y-6 w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Supplier LCA Data</h2>
          <p className="text-muted-foreground">
            Track and manage supplier lifecycle assessment information
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="professional" onClick={onExport}>
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="corporate" onClick={onAddNew}>
            <Plus className="h-4 w-4" />
            Add Supplier
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search suppliers, parts, or codes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background text-foreground"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="overdue">Overdue</option>
                <option value="not-started">Not Started</option>
              </select>
              <Button
                variant="outline"
                onClick={clearAllFilters}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          <div className="w-full">
            <table className="w-full table-fixed">
              <thead className="border-b bg-muted/50">
                <tr>
                  <th className="text-left p-3 font-medium w-[15%] relative">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <span>Supplier</span>
                      </div>
                      <button
                        onClick={() => toggleFilterDropdown('supplier')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.supplier ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.supplier}
                      onClose={() => toggleFilterDropdown('supplier')}
                    >
                      <Input
                        placeholder="Filter suppliers..."
                        value={columnFilters.supplier}
                        onChange={(e) => handleColumnFilterChange('supplier', e.target.value)}
                        className="h-8 text-xs"
                      />
                    </FilterDropdown>
                  </th>
                  <th className="text-left p-3 font-medium w-[10%] relative">
                    <div className="flex items-center justify-between">
                      <span>Vendor Code</span>
                      <button
                        onClick={() => toggleFilterDropdown('vendorCode')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.vendorCode ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.vendorCode}
                      onClose={() => toggleFilterDropdown('vendorCode')}
                    >
                      <Input
                        placeholder="Filter codes..."
                        value={columnFilters.vendorCode}
                        onChange={(e) => handleColumnFilterChange('vendorCode', e.target.value)}
                        className="h-8 text-xs"
                      />
                    </FilterDropdown>
                  </th>
                  <th className="text-left p-3 font-medium w-[12%] relative">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        <span>Part Number</span>
                      </div>
                      <button
                        onClick={() => toggleFilterDropdown('partNumber')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.partNumber ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.partNumber}
                      onClose={() => toggleFilterDropdown('partNumber')}
                    >
                      <Input
                        placeholder="Filter parts..."
                        value={columnFilters.partNumber}
                        onChange={(e) => handleColumnFilterChange('partNumber', e.target.value)}
                        className="h-8 text-xs"
                      />
                    </FilterDropdown>
                  </th>
                  <th className="text-left p-3 font-medium w-[20%] relative">
                    <div className="flex items-center justify-between">
                      <span>Description</span>
                      <button
                        onClick={() => toggleFilterDropdown('description')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.description ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.description}
                      onClose={() => toggleFilterDropdown('description')}
                    >
                      <Input
                        placeholder="Filter descriptions..."
                        value={columnFilters.description}
                        onChange={(e) => handleColumnFilterChange('description', e.target.value)}
                        className="h-8 text-xs"
                      />
                    </FilterDropdown>
                  </th>
                  <th className="text-left p-3 font-medium w-[10%] relative">
                    <div className="flex items-center justify-between">
                      <span>IMDS ID</span>
                      <button
                        onClick={() => toggleFilterDropdown('imdsId')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.imdsId ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.imdsId}
                      onClose={() => toggleFilterDropdown('imdsId')}
                    >
                      <Input
                        placeholder="Filter IMDS..."
                        value={columnFilters.imdsId}
                        onChange={(e) => handleColumnFilterChange('imdsId', e.target.value)}
                        className="h-8 text-xs"
                      />
                    </FilterDropdown>
                  </th>
                  <th className="text-left p-3 font-medium w-[10%] relative">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Last Updated</span>
                      </div>
                      <button
                        onClick={() => toggleFilterDropdown('lastUpdated')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.lastUpdated ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.lastUpdated}
                      onClose={() => toggleFilterDropdown('lastUpdated')}
                    >
                      <Input
                        placeholder="Filter dates..."
                        value={columnFilters.lastUpdated}
                        onChange={(e) => handleColumnFilterChange('lastUpdated', e.target.value)}
                        className="h-8 text-xs"
                      />
                    </FilterDropdown>
                  </th>
                  <th className="text-left p-3 font-medium w-[23%] relative">
                    <div className="flex items-center justify-between">
                      <span>Data Collection Status</span>
                      <button
                        onClick={() => toggleFilterDropdown('dataCollectionStatus')}
                        className={`p-1 rounded hover:bg-gray-100 ${columnFilters.dataCollectionStatus ? 'text-blue-600' : 'text-gray-400'}`}
                      >
                        <Filter className="h-4 w-4" />
                      </button>
                    </div>
                    <FilterDropdown
                      isOpen={filterDropdowns.dataCollectionStatus}
                      onClose={() => toggleFilterDropdown('dataCollectionStatus')}
                    >
                      <select
                        value={columnFilters.dataCollectionStatus}
                        onChange={(e) => handleColumnFilterChange('dataCollectionStatus', e.target.value)}
                        className="w-full h-8 text-xs border border-input bg-background px-2 rounded-md"
                      >
                        <option value="">All Status</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="overdue">Overdue</option>
                        <option value="not-started">Not Started</option>
                      </select>
                    </FilterDropdown>
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedData.map((item) => {
                  return (
                    <tr
                      key={item.id}
                      className="border-b hover:bg-muted/50 transition-colors"
                    >
                      <td className="p-3 font-medium truncate">{item.supplier}</td>
                      <td className="p-3 font-mono text-sm">{item.vendorCode}</td>
                      <td className="p-3 font-mono text-sm">{item.partNumber}</td>
                      <td className="p-3 truncate" title={item.description}>
                        {item.description}
                      </td>
                      <td className="p-3 font-mono text-sm">{item.imdsId || '-'}</td>
                      <td className="p-3 text-sm">
                        {item.lastUpdated.toLocaleDateString()}
                      </td>
                      <td className="p-3">
                        <DataCollectionStatusDisplay status={item.dataCollectionStatus} />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center">
          <p className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
            {Math.min(currentPage * itemsPerPage, filteredData.length)} of{' '}
            {filteredData.length} entries
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};