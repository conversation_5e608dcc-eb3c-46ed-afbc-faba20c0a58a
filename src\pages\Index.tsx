import React from 'react';
import { useToast } from '@/hooks/use-toast';
import { SupplierDataTable } from '@/components/SupplierDataTable';
import { DashboardStats } from '@/components/DashboardStats';
import { sampleSupplierData } from '@/data/sampleSupplierData';
import { Card, CardContent } from '@/components/ui/card';
import heroImage from '@/assets/hero-banner.jpg';

const Index = () => {
  const { toast } = useToast();

  const handleAddNew = () => {
    toast({
      title: "Add New Supplier",
      description: "New supplier form would open here",
    });
  };

  const handleExport = () => {
    toast({
      title: "Export Data",
      description: "Data exported successfully to CSV",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted">
      {/* Hero Section */}
      {/* <div className="relative overflow-hidden">
        <div 
          className="h-64 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${heroImage})` }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-secondary/80" />
          <div className="relative z-10 container mx-auto px-6 py-16">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                Supplier LCA Tracker
              </h1>
              <p className="text-xl text-white/90 mb-6">
                Comprehensive lifecycle assessment data management for automotive suppliers and parts
              </p>
              <div className="flex flex-wrap gap-4 text-white/80 text-sm">
                <span className="flex items-center gap-2">
                  ✓ Real-time data tracking
                </span>
                <span className="flex items-center gap-2">
                  ✓ IMDS integration
                </span>
                <span className="flex items-center gap-2">
                  ✓ Compliance monitoring
                </span>
              </div>
            </div>
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <div className="container mx-auto px-6 space-y-8">
        {/* Dashboard Stats */}
        <div className="w-full">
          <DashboardStats data={sampleSupplierData} />
        </div>

        {/* Additional Main Content Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Quick Actions Card */}
          <Card className="border-l-4 border-l-blue-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Quick Actions</h3>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">⚡</span>
                </div>
              </div>
              <div className="space-y-3">
                <button
                  onClick={handleAddNew}
                  className="w-full text-left p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <div className="font-medium">Add New Supplier</div>
                  <div className="text-sm text-gray-600">Register a new supplier in the system</div>
                </button>
                <button
                  onClick={handleExport}
                  className="w-full text-left p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <div className="font-medium">Export Data</div>
                  <div className="text-sm text-gray-600">Download current data as CSV</div>
                </button>
              </div>
            </CardContent>
          </Card>

          {/* System Overview Card */}
          <Card className="border-l-4 border-l-purple-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">System Overview</h3>
                <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 text-sm">📊</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Data Quality Score</span>
                  <span className="font-semibold text-green-600">85%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Last Sync</span>
                  <span className="font-semibold">2 hours ago</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Active Users</span>
                  <span className="font-semibold">12</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-4">
                  <div className="bg-purple-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Supplier Data Table - Full Width */}
        <div className="w-full">
          <SupplierDataTable
            data={sampleSupplierData}
            onAddNew={handleAddNew}
            onExport={handleExport}
          />
        </div>
      </div>
    </div>
  );
};

export default Index;
